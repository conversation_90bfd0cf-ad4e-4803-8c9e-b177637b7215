/**
 * 邀请码核销系统 - 控制台交互逻辑
 */

// 全局变量
let currentPage = 1;
let totalPages = 1;
let currentStatus = 'all';
let userData = null;
let currentUserPage = 1;
let totalUserPages = 1;

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 检查登录状态
    checkAuth();
    
    // 导航切换
    setupNavigation();
    
    // 退出登录
    const logoutButton = document.getElementById('btn-logout');
    if (logoutButton) {
        logoutButton.addEventListener('click', logout);
    } else {
        console.error('找不到退出按钮元素');
    }
    
    // 表单提交处理
    setupFormHandlers();
    
    // 初始化过滤器
    setupFilters();
});

/**
 * 检查用户认证状态
 */
function checkAuth() {
    // 直接通过 API 调用检查登录状态，而不是检查 localStorage
    fetchUserProfile();
}

/**
 * 获取用户个人信息
 */
function fetchUserProfile() {
    fetch('/api/auth/profile', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin' // 包含 cookies
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('认证失败');
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            userData = data.data;
            
            // 更新用户名显示
            const userNameElement = document.getElementById('user-name');
            if (userNameElement) {
                userNameElement.textContent = userData.username;
            } else {
                console.error('找不到用户名显示元素');
            }
            
            // 更新个人信息表单
            if (document.getElementById('profile-username')) {
                document.getElementById('profile-username').value = userData.username;
                document.getElementById('profile-email').value = userData.email;
            }
            
            // 保存用户信息到 sessionStorage（不包含敏感信息）
            sessionStorage.setItem('user', JSON.stringify(userData));
            
            // 如果是管理员，显示用户管理菜单
            if (userData.is_admin) {
                console.log('当前用户是管理员，显示用户管理菜单');
                const usersNav = document.getElementById('users-nav');
                if (usersNav) {
                    usersNav.style.display = 'block';
                } else {
                    console.error('找不到用户管理菜单元素');
                }
            } else {
                console.log('当前用户不是管理员，隐藏用户管理菜单');
            }
            
            // 加载仪表盘数据
            loadDashboardData();
            
            // 更新生成数量限制
            updateGenerateCountLimit();
        } else {
            throw new Error(data.error || '获取用户信息失败');
        }
    })
    .catch(error => {
        console.error('获取用户信息错误:', error);
        // 认证失败，清除本地存储并跳转到登录页
        sessionStorage.removeItem('user');
        window.location.href = '/login';
    });
}

/**
 * 加载仪表盘数据
 */
function loadDashboardData() {
    // 获取当前激活的section
    const activeSection = document.querySelector('.content-section.active');
    if (!activeSection) {
        // 如果没有激活的section，加载默认的仪表盘数据
        loadCodeStats();
        loadRecentCodes();
        return;
    }
    
    const sectionId = activeSection.id;
    
    // 根据不同的section加载不同的数据
    switch (sectionId) {
        case 'dashboard-section':
            loadCodeStats();
            loadRecentCodes();
            break;
        case 'invite-codes-section':
            loadInviteCodes();
            break;
        case 'redeem-section':
            loadRedeemedCodes();
            break;
        case 'api-keys-section':
            loadApiKeys();
            break;
        case 'users-section':
            if (userData && userData.is_admin) {
                loadUsers();
            }
            break;
        case 'profile-section':
            // 个人信息在fetchUserProfile中已经加载
            break;
    }
}

/**
 * 加载邀请码统计数据
 */
function loadCodeStats() {
    // 使用专门的统计API接口，一次性获取所有统计数据
    fetch('/api/invite/stats', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin' // 包含 cookies
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const stats = data.data;
            
            // 更新统计数据显示
            const totalCodesElement = document.getElementById('total-codes');
            const usedCodesElement = document.getElementById('used-codes');
            const unusedCodesElement = document.getElementById('unused-codes');
            const usageRateElement = document.getElementById('usage-rate');
            
            if (totalCodesElement) totalCodesElement.textContent = stats.total_codes || 0;
            if (usedCodesElement) usedCodesElement.textContent = stats.used_codes || 0;
            if (unusedCodesElement) unusedCodesElement.textContent = stats.unused_codes || 0;
            if (usageRateElement) usageRateElement.textContent = (stats.usage_rate || 0) + '%';
        }
    })
    .catch(error => {
        console.error('加载统计数据错误:', error);
        // 显示错误状态
        const elements = ['total-codes', 'used-codes', 'unused-codes', 'usage-rate'];
        elements.forEach(id => {
            const element = document.getElementById(id);
            if (element) element.textContent = 'Error';
        });
    });
}

/**
 * 加载最近邀请码
 */
function loadRecentCodes() {
    fetch('/api/invite/codes?per_page=5', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin' // 包含 cookies
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.data.codes) {
            const codesTable = document.getElementById('recent-codes-table');
            
            if (!codesTable) {
                console.error('找不到最近邀请码表格元素');
                return;
            }
            
            if (data.data.codes.length === 0) {
                codesTable.innerHTML = '<tr><td colspan="4" style="text-align: center;">暂无邀请码</td></tr>';
                return;
            }
            
            let html = '';
            data.data.codes.forEach(code => {
                let status = '';
                if (code.is_used) {
                    status = '<span class="badge badge-success">已使用</span>';
                } else if (new Date(code.expires_at) < new Date()) {
                    status = '<span class="badge badge-danger">已过期</span>';
                } else {
                    status = '<span class="badge badge-warning">未使用</span>';
                }
                
                html += `
                    <tr>
                        <td>${code.code}</td>
                        <td>${formatDate(code.created_at)}</td>
                        <td>${formatDate(code.expires_at)}</td>
                        <td>${status}</td>
                    </tr>
                `;
            });
            
            codesTable.innerHTML = html;
        }
    })
    .catch(error => {
        console.error('加载最近邀请码错误:', error);
        document.getElementById('recent-codes-table').innerHTML = 
            '<tr><td colspan="4" style="text-align: center;">加载失败</td></tr>';
    });
}

/**
 * 加载邀请码列表
 */
function loadInviteCodes() {
    const codesTable = document.getElementById('codes-table');
    if (!codesTable) {
        console.error('找不到邀请码列表表格元素');
        return;
    }
    
    codesTable.innerHTML = '<tr><td colspan="8" style="text-align: center;">加载中...</td></tr>';
    
    let url = `/api/invite/codes?page=${currentPage}&per_page=10&status=${currentStatus}`;
    
    fetch(url, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.data.codes) {
            if (data.data.codes.length === 0) {
                codesTable.innerHTML = '<tr><td colspan="8" style="text-align: center;">暂无邀请码</td></tr>';
                document.getElementById('codes-pagination').innerHTML = '';
                return;
            }
            
            let html = '';
            data.data.codes.forEach(code => {
                let status = '';
                let canDelete = false;
                
                if (code.is_used) {
                    status = '<span class="badge badge-success">已使用</span>';
                } else if (new Date(code.expires_at) < new Date()) {
                    status = '<span class="badge badge-danger">已过期</span>';
                    canDelete = true;
                } else {
                    status = '<span class="badge badge-warning">未使用</span>';
                    canDelete = true;
                }
                
                html += `
                    <tr>
                        <td><input type="checkbox" class="code-checkbox" value="${code.id}" ${!canDelete ? 'disabled' : ''}></td>
                        <td class="code-display">${code.code}</td>
                        <td>${formatDate(code.created_at)}</td>
                        <td>${formatDate(code.expires_at)}</td>
                        <td>${status}</td>
                        <td>${code.used_by_id ? '已使用' : '-'}</td>
                        <td>${code.redeemed_at ? formatDate(code.redeemed_at) : '-'}</td>
                        <td class="actions">
                            <button class="btn btn-sm btn-primary" onclick="copyInviteCode('${code.code}')" title="复制邀请码">
                                📋
                            </button>
                            ${canDelete ? `<button class="btn btn-sm btn-danger" onclick="deleteInviteCode(${code.id}, '${code.code}')" title="删除邀请码">🗑️</button>` : ''}
                        </td>
                    </tr>
                `;
            });
            
            codesTable.innerHTML = html;
            
            // 更新分页
            totalPages = data.data.pages;
            updatePagination();
            
            // 更新批量操作按钮状态
            updateBatchActions();
        }
    })
    .catch(error => {
        console.error('加载邀请码列表错误:', error);
        codesTable.innerHTML = '<tr><td colspan="8" style="text-align: center;">加载失败</td></tr>';
    });
}

/**
 * 加载核销记录
 */
function loadRedeemedCodes(page = 1, status = 'all') {
    const params = new URLSearchParams({
        status: 'used',
        page: page,
        per_page: 10
    });

    fetch(`/api/invite/codes?${params}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.data.codes) {
            const redeemedTable = document.getElementById('redeemed-codes-table');

            if (!redeemedTable) {
                console.error('找不到核销记录表格元素');
                return;
            }

            if (data.data.codes.length === 0) {
                redeemedTable.innerHTML = '<tr><td colspan="6" style="text-align: center;">暂无核销记录</td></tr>';
                return;
            }

            let html = '';
            data.data.codes.forEach(code => {
                // 判断核销方式
                const redeemMethod = code.used_by_id ? '用户核销' : 'API核销';
                const creatorName = code.creator_name || '未知';

                html += `
                    <tr>
                        <td><input type="checkbox" class="redeem-checkbox" value="${code.id}"></td>
                        <td class="code-display">${code.code}</td>
                        <td>${formatDate(code.redeemed_at)}</td>
                        <td>${redeemMethod}</td>
                        <td>${creatorName}</td>
                        <td class="actions">
                            <button class="btn btn-sm btn-danger" onclick="deleteRedeemCode(${code.id}, '${code.code}')" title="删除核销记录">
                                🗑️
                            </button>
                        </td>
                    </tr>
                `;
            });

            redeemedTable.innerHTML = html;

            // 更新分页
            updateRedeemPagination(data.data.pages, page);

            // 更新批量操作按钮状态
            updateRedeemBatchActions();
        }
    })
    .catch(error => {
        console.error('加载核销记录错误:', error);
        document.getElementById('redeemed-codes-table').innerHTML =
            '<tr><td colspan="6" style="text-align: center;">加载失败</td></tr>';
    });
}

/**
 * 加载用户列表
 */
function loadUsers() {
    const usersTable = document.getElementById('users-table');
    if (!usersTable) {
        console.error('找不到用户列表表格元素');
        return;
    }
    
    usersTable.innerHTML = '<tr><td colspan="5" style="text-align: center;">加载中...</td></tr>';
    
    let url = `/api/auth/users?page=${currentUserPage}`;
    
    fetch(url, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin' // 包含 cookies
    })
    .then(response => {
        if (!response.ok) {
            console.error(`提取 加载失败:${response.status} "${response.url}".`);
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success && data.data.users) {
            if (data.data.users.length === 0) {
                usersTable.innerHTML = '<tr><td colspan="5" style="text-align: center;">暂无用户</td></tr>';
                document.getElementById('users-pagination').innerHTML = '';
                return;
            }

            let html = '';
            data.data.users.forEach(user => {
                // 检查用户角色
                let isAdmin = user.is_admin;
                if (user.roles && user.roles.length > 0) {
                    isAdmin = user.roles.some(role => role.name === 'admin');
                }

                const statusBadge = isAdmin ?
                    '<span class="badge badge-success">管理员</span>' :
                    '<span class="badge badge-warning">普通用户</span>';

                html += `
                    <tr>
                        <td>${user.username}</td>
                        <td>${user.email}</td>
                        <td>${formatDate(user.created_at)}</td>
                        <td>${statusBadge}</td>
                        <td>
                            <button class="btn btn-secondary" onclick="editUser(${user.id})">编辑</button>
                            ${user.id !== userData.id ? `<button class="btn btn-danger" onclick="deleteUser(${user.id}, '${user.username}')">删除</button>` : ''}
                        </td>
                    </tr>
                `;
            });

            usersTable.innerHTML = html;

            // 更新分页
            totalUserPages = data.data.pages;
            updateUserPagination();
        } else {
            console.error('API 响应错误:', data);
            usersTable.innerHTML = '<tr><td colspan="5" style="text-align: center;">加载失败: ' + (data.error || '未知错误') + '</td></tr>';
        }
    })
    .catch(error => {
        console.error('加载用户列表错误:', error);
        usersTable.innerHTML = '<tr><td colspan="5" style="text-align: center;">加载失败: ' + error.message + '</td></tr>';
    });
}

/**
 * 更新分页
 */
function updatePagination() {
    const pagination = document.getElementById('codes-pagination');
    if (!pagination) return;
    
    let html = '';
    
    // 上一页按钮
    if (currentPage > 1) {
        html += `<button onclick="changePage(${currentPage - 1})">上一页</button>`;
    }
    
    // 页码按钮
    for (let i = 1; i <= totalPages; i++) {
        if (i === currentPage) {
            html += `<button class="active">${i}</button>`;
        } else {
            html += `<button onclick="changePage(${i})">${i}</button>`;
        }
    }
    
    // 下一页按钮
    if (currentPage < totalPages) {
        html += `<button onclick="changePage(${currentPage + 1})">下一页</button>`;
    }
    
    pagination.innerHTML = html;
}

/**
 * 更新用户分页
 */
function updateUserPagination() {
    const pagination = document.getElementById('users-pagination');
    if (!pagination) return;
    
    let html = '';
    
    // 上一页按钮
    if (currentUserPage > 1) {
        html += `<button onclick="changeUserPage(${currentUserPage - 1})">上一页</button>`;
    }
    
    // 页码按钮
    for (let i = 1; i <= totalUserPages; i++) {
        if (i === currentUserPage) {
            html += `<button class="active">${i}</button>`;
        } else {
            html += `<button onclick="changeUserPage(${i})">${i}</button>`;
        }
    }
    
    // 下一页按钮
    if (currentUserPage < totalUserPages) {
        html += `<button onclick="changeUserPage(${currentUserPage + 1})">下一页</button>`;
    }
    
    pagination.innerHTML = html;
}

/**
 * 切换页面
 */
function changePage(page) {
    currentPage = page;
    loadInviteCodes();
}

/**
 * 切换用户页面
 */
function changeUserPage(page) {
    currentUserPage = page;
    loadUsers();
}

/**
 * 设置导航
 */
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const sections = document.querySelectorAll('.content-section');
    const pageTitle = document.getElementById('page-title');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // 移除所有活动状态
            navLinks.forEach(nav => nav.classList.remove('active'));
            sections.forEach(section => section.classList.remove('active'));
            
            // 添加活动状态
            this.classList.add('active');
            
            // 显示对应的内容区域
            const targetSection = document.getElementById(this.dataset.section);
            if (targetSection) {
                targetSection.classList.add('active');
                
                // 更新页面标题
                const titles = {
                    'dashboard-section': '仪表盘',
                    'invite-codes-section': '邀请码管理',
                    'redeem-section': '核销邀请码',
                    'api-keys-section': 'API 管理',
                    'users-section': '用户管理',
                    'profile-section': '个人设置'
                };
                
                pageTitle.textContent = titles[this.dataset.section] || '控制台';
                
                // 重新加载对应页面的数据
                loadDashboardData();
            }
        });
    });
}

/**
 * 设置表单处理器
 */
function setupFormHandlers() {
    // 生成邀请码表单
    const generateForm = document.getElementById('generate-code-form');
    if (generateForm) {
        generateForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const expiryDays = parseInt(document.getElementById('expiry-days').value);
            const count = parseInt(document.getElementById('generate-count').value);
            
            // 验证输入
            if (!expiryDays || expiryDays < 1 || expiryDays > 30) {
                showToast('有效期必须在1-30天之间', 'warning');
                return;
            }
            
            // 根据用户权限调整限制
            const isAdmin = userData && userData.is_admin;
            const maxCount = isAdmin ? 50 : 20;
            
            if (!count || count < 1 || count > maxCount) {
                showToast(`生成数量必须在1-${maxCount}个之间`, 'warning');
                return;
            }
            
            generateInviteCode(expiryDays, count);
        });
        
        // 动态调整生成数量输入框的最大值
        updateGenerateCountLimit();
    }
    
    // 核销邀请码表单
    const redeemForm = document.getElementById('redeem-code-form');
    if (redeemForm) {
        redeemForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const code = document.getElementById('invite-code').value;
            if (code) {
                redeemInviteCode(code);
            }
        });
    }
    
    // 个人信息表单
    const profileForm = document.getElementById('profile-form');
    if (profileForm) {
        profileForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = document.getElementById('profile-email').value;
            updateProfile({ email });
        });
    }
    
    // 修改密码表单
    const passwordForm = document.getElementById('password-form');
    if (passwordForm) {
        passwordForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const currentPassword = document.getElementById('current-password').value;
            const newPassword = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;
            
            if (newPassword !== confirmPassword) {
                alert('新密码和确认密码不匹配');
                return;
            }
            
            updatePassword(currentPassword, newPassword);
        });
    }

    // 创建API Key表单
    const createApiKeyForm = document.getElementById('create-api-key-form');
    if (createApiKeyForm) {
        createApiKeyForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const name = document.getElementById('api-key-name').value;
            if (name) {
                createApiKey(name);
            }
        });
    }
    
    // 添加用户表单
    const addUserForm = document.getElementById('add-user-form');
    if (addUserForm) {
        addUserForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('new-username').value;
            const email = document.getElementById('new-email').value;
            const password = document.getElementById('new-password').value;
            const isAdmin = document.getElementById('new-is-admin').checked;
            
            addUser(username, email, password, isAdmin);
        });
    }
    
    // 编辑用户表单
    const editUserForm = document.getElementById('edit-user-form');
    if (editUserForm) {
        editUserForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const userId = document.getElementById('edit-user-id').value;
            const email = document.getElementById('edit-email').value;
            const isAdmin = document.getElementById('edit-is-admin').checked;
            const password = document.getElementById('edit-new-password').value;
            
            const updateData = {
                email: email,
                is_admin: isAdmin
            };
            
            if (password) {
                updateData.password = password;
            }
            
            updateUser(userId, updateData);
        });
    }
}

/**
 * 更新生成数量限制
 */
function updateGenerateCountLimit() {
    const generateCountInput = document.getElementById('generate-count');
    const helpText = generateCountInput.nextElementSibling;
    
    if (generateCountInput && userData) {
        const isAdmin = userData.is_admin;
        const maxCount = isAdmin ? 50 : 20;
        
        generateCountInput.max = maxCount;
        
        if (helpText) {
            helpText.textContent = `单次最多生成${maxCount}个${isAdmin ? '（管理员）' : '（普通用户）'}`;
        }
    }
}

/**
 * 设置过滤器
 */
function setupFilters() {
    const statusFilter = document.getElementById('code-status-filter');
    if (statusFilter) {
        statusFilter.addEventListener('change', function() {
            currentStatus = this.value;
            currentPage = 1;
            loadInviteCodes();
        });
    }
    
    // 添加批量操作事件监听器
    setupBatchOperations();
}

/**
 * 设置批量操作事件监听器
 */
function setupBatchOperations() {
    // 为动态添加的复选框添加事件监听器
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('code-checkbox')) {
            updateBatchActions();
        }
    });
    
    // 头部全选复选框事件
    const selectAllHeader = document.getElementById('select-all-header');
    if (selectAllHeader) {
        selectAllHeader.addEventListener('change', function() {
            toggleAllCodes(this.checked);
        });
    }
}

/**
 * 生成邀请码（支持批量生成）
 */
function generateInviteCode(expiryDays, count = 1) {
    const generateBtn = document.querySelector('#generate-code-form button[type="submit"]');
    const originalText = generateBtn.innerHTML;
    
    // 显示加载状态
    generateBtn.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div> 生成中...';
    generateBtn.disabled = true;
    
    fetch('/api/invite/generate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ 
            expiry_days: parseInt(expiryDays),
            count: parseInt(count)
        }),
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (count === 1) {
                // 单个生成
                showToast(`邀请码生成成功: ${data.data.code}`, 'success');
            } else {
                // 批量生成
                showToast(data.message, 'success');
                showBatchGenerateResult(data.data);
            }
            
            // 重新加载数据
            loadDashboardData();
        } else {
            showToast(`生成失败: ${data.error}`, 'error');
        }
    })
    .catch(error => {
        console.error('生成邀请码错误:', error);
        showToast('生成邀请码失败，请稍后重试', 'error');
    })
    .finally(() => {
        // 恢复按钮状态
        generateBtn.innerHTML = originalText;
        generateBtn.disabled = false;
    });
}

/**
 * 显示批量生成结果
 */
function showBatchGenerateResult(data) {
    const resultDiv = document.getElementById('batch-generate-result');
    const countSpan = document.getElementById('batch-result-count');
    const codesList = document.getElementById('batch-codes-list');
    
    // 更新计数
    countSpan.textContent = data.count;
    
    // 生成邀请码列表
    let html = '';
    data.codes.forEach((code, index) => {
        html += `
            <div class="batch-code-item">
                <span class="batch-code-text">${code.code}</span>
                <button class="btn btn-sm btn-outline-primary" onclick="copyInviteCode('${code.code}')" title="复制">
                    📋
                </button>
            </div>
        `;
    });
    
    codesList.innerHTML = html;
    
    // 显示结果区域
    resultDiv.style.display = 'block';
    
    // 设置事件监听器
    setupBatchResultEvents(data.codes);
}

/**
 * 设置批量结果事件监听器
 */
function setupBatchResultEvents(codes) {
    const copyAllBtn = document.getElementById('copy-all-codes');
    const closeBtn = document.getElementById('close-batch-result');
    
    // 复制全部按钮
    copyAllBtn.onclick = () => {
        const allCodes = codes.map(code => code.code).join('\n');
        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(allCodes).then(() => {
                showToast('所有邀请码已复制到剪贴板', 'success');
            }).catch(err => {
                console.error('复制失败:', err);
                fallbackCopyText(allCodes);
            });
        } else {
            fallbackCopyText(allCodes);
        }
    };
    
    // 关闭按钮
    closeBtn.onclick = () => {
        document.getElementById('batch-generate-result').style.display = 'none';
    };
}

/**
 * 核销邀请码
 */
function redeemInviteCode(code) {
    const resultEl = document.getElementById('redeem-result');
    
    resultEl.innerHTML = '<div class="loader"></div>';
    
    fetch('/api/invite/redeem', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ code }),
        credentials: 'same-origin' // 包含 cookies
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultEl.innerHTML = `
                <div style="background-color: #e8f5e9; color: #2e7d32; padding: 15px; border-radius: 4px;">
                    <h3>核销成功</h3>
                    <p>邀请码: ${data.data.code}</p>
                    <p>核销时间: ${formatDate(data.data.redeemed_at)}</p>
                </div>
            `;
            
            // 清空输入框
            document.getElementById('invite-code').value = '';
            
            // 重新加载数据
            loadDashboardData();
        } else {
            resultEl.innerHTML = `
                <div style="background-color: #ffebee; color: #c62828; padding: 15px; border-radius: 4px;">
                    <h3>核销失败</h3>
                    <p>${data.error}</p>
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('核销邀请码错误:', error);
        resultEl.innerHTML = `
            <div style="background-color: #ffebee; color: #c62828; padding: 15px; border-radius: 4px;">
                <h3>核销失败</h3>
                <p>网络错误，请稍后重试</p>
            </div>
        `;
    });
}

/**
 * 更新个人信息
 */
function updateProfile(data) {
    fetch('/api/auth/profile', {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(data),
        credentials: 'same-origin' // 包含 cookies
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('个人信息更新成功');
            
            // 更新本地存储的用户信息
            userData = data.data;
            sessionStorage.setItem('user', JSON.stringify(userData));
        } else {
            alert(`更新失败: ${data.error}`);
        }
    })
    .catch(error => {
        console.error('更新个人信息错误:', error);
        alert('更新个人信息失败，请稍后重试');
    });
}

/**
 * 更新密码
 */
function updatePassword(currentPassword, newPassword) {
    // 这里假设后端提供了修改密码的接口
    // 实际项目中可能需要调整接口路径和参数
    fetch('/api/auth/change-password', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            current_password: currentPassword,
            password: newPassword
        }),
        credentials: 'same-origin' // 包含 cookies
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('密码修改成功，请重新登录');
            logout();
        } else {
            alert(`修改失败: ${data.error}`);
        }
    })
    .catch(error => {
        console.error('修改密码错误:', error);
        alert('修改密码失败，请稍后重试');
    });
}

/**
 * 添加用户
 */
function addUser(username, email, password, isAdmin = false) {
    // 使用管理员API端点创建用户
    fetch('/api/auth/users', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({
            username: username,
            email: email,
            password: password,
            is_admin: isAdmin
        }),
        credentials: 'same-origin' // 包含 cookies
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('用户添加成功');
            
            // 清空表单
            document.getElementById('add-user-form').reset();
            
            // 隐藏添加用户卡片
            document.getElementById('add-user-card').style.display = 'none';
            
            // 重新加载用户列表
            loadUsers();
        } else {
            alert(`添加失败: ${data.error}`);
        }
    })
    .catch(error => {
        console.error('添加用户错误:', error);
        alert('添加用户失败，请稍后重试');
    });
}

/**
 * 编辑用户
 */
function editUser(userId) {
    fetch(`/api/auth/users/${userId}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin' // 包含 cookies
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const user = data.data;
            
            // 检查用户是否为管理员（通过角色或is_admin字段）
            let isAdmin = user.is_admin;
            if (user.roles && user.roles.length > 0) {
                isAdmin = user.roles.some(role => role.name === 'admin');
            }
            
            // 填充表单
            document.getElementById('edit-user-id').value = user.id;
            document.getElementById('edit-username').value = user.username;
            document.getElementById('edit-email').value = user.email;
            document.getElementById('edit-is-admin').checked = isAdmin;
            document.getElementById('edit-new-password').value = '';
            
            // 显示模态框
            document.getElementById('user-edit-modal').style.display = 'flex';
        } else {
            alert(`获取用户信息失败: ${data.error}`);
        }
    })
    .catch(error => {
        console.error('获取用户信息错误:', error);
        alert('获取用户信息失败，请稍后重试');
    });
}

/**
 * 更新用户
 */
function updateUser(userId, updateData) {
    fetch(`/api/auth/users/${userId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify(updateData),
        credentials: 'same-origin' // 包含 cookies
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('用户信息更新成功');
            
            // 关闭模态框
            closeUserEditModal();
            
            // 重新加载用户列表
            loadUsers();
        } else {
            alert(`更新失败: ${data.error}`);
        }
    })
    .catch(error => {
        console.error('更新用户错误:', error);
        alert('更新用户失败，请稍后重试');
    });
}

/**
 * 删除用户
 */
function deleteUser(userId, username) {
    if (!confirm(`确定要删除用户 "${username}" 吗？此操作不可恢复。`)) {
        return;
    }
    
    fetch(`/api/auth/users/${userId}`, {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin' // 包含 cookies
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('用户删除成功');
            
            // 重新加载用户列表
            loadUsers();
        } else {
            alert(`删除失败: ${data.error}`);
        }
    })
    .catch(error => {
        console.error('删除用户错误:', error);
        alert('删除用户失败，请稍后重试');
    });
}

/**
 * 关闭用户编辑模态框
 */
function closeUserEditModal() {
    document.getElementById('user-edit-modal').style.display = 'none';
}

/**
 * 退出登录
 */
function logout() {
    // 清除本地存储
    sessionStorage.removeItem('user');
    
    // 调用后端注销接口（可选）
    fetch('/api/auth/logout', {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin'
    })
    .then(() => {
        // 跳转到登录页
        window.location.href = '/login';
    })
    .catch(() => {
        // 即使注销接口失败，也跳转到登录页
        window.location.href = '/login';
    });
}

/**
 * 格式化日期
 */
function formatDate(dateString) {
    if (!dateString) return '-';
    
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

/**
 * 创建API Key
 */
function createApiKey(name) {
    fetch('/api/keys', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ name }),
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('API Key创建成功！\n\nAPI Key: ' + data.data.key + '\n\n请妥善保管，此密钥不会再次显示。');
            document.getElementById('create-api-key-form').reset();
            loadApiKeys();
        } else {
            alert('API Key创建失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('创建API Key错误:', error);
        alert('网络错误，请稍后重试');
    });
}

/**
 * 加载API Key列表
 */
function loadApiKeys() {
    fetch('/api/keys', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.data.keys) {
            const apiKeysTable = document.getElementById('api-keys-table');
            
            if (!apiKeysTable) {
                console.error('找不到API Key表格元素');
                return;
            }
            
            if (data.data.keys.length === 0) {
                apiKeysTable.innerHTML = '<tr><td colspan="7" style="text-align: center;">暂无API Key</td></tr>';
                return;
            }
            
            let html = '';
            data.data.keys.forEach(key => {
                const status = key.is_active ? 
                    '<span class="badge badge-success">启用</span>' : 
                    '<span class="badge badge-danger">禁用</span>';
                
                const maskedKey = key.key.substring(0, 8) + '****' + key.key.substring(key.key.length - 4);
                
                html += `
                    <tr>
                        <td>${key.name}</td>
                        <td>
                            <span class="api-key-display">${maskedKey}</span>
                            <button class="btn btn-sm" onclick="copyApiKey('${key.key}')">复制</button>
                        </td>
                        <td>${status}</td>
                        <td>${key.invite_codes_count}</td>
                        <td>${formatDate(key.created_at)}</td>
                        <td>${key.last_used_at ? formatDate(key.last_used_at) : '未使用'}</td>
                        <td>
                            ${key.is_active ?
                                `<button class="btn btn-sm btn-primary" onclick="showApiBatchGenerateModal(${key.id}, '${key.name}', '${key.key}')">批量生成</button>` :
                                `<button class="btn btn-sm btn-primary" disabled title="API Key已禁用">批量生成</button>`
                            }
                            <button class="btn btn-sm" onclick="viewApiKeyCodes(${key.id})">查看邀请码</button>
                            <button class="btn btn-sm" onclick="createApiKeyCode(${key.id})">生成邀请码</button>
                            <button class="btn btn-sm" onclick="toggleApiKey(${key.id}, ${!key.is_active})">${key.is_active ? '禁用' : '启用'}</button>
                            <button class="btn btn-sm" onclick="deleteApiKey(${key.id})">删除</button>
                        </td>
                    </tr>
                `;
            });
            
            apiKeysTable.innerHTML = html;
        }
    })
    .catch(error => {
        console.error('加载API Key列表错误:', error);
        document.getElementById('api-keys-table').innerHTML = 
            '<tr><td colspan="7" style="text-align: center;">加载失败</td></tr>';
    });
}

/**
 * 复制API Key
 */
function copyApiKey(key) {
    navigator.clipboard.writeText(key).then(() => {
        alert('API Key已复制到剪贴板');
    }).catch(err => {
        console.error('复制失败:', err);
        alert('复制失败，请手动复制');
    });
}

/**
 * 切换API Key状态
 */
function toggleApiKey(keyId, isActive) {
    fetch(`/api/keys/${keyId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ is_active: isActive }),
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('API Key状态更新成功');
            loadApiKeys();
        } else {
            alert('API Key状态更新失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('更新API Key状态错误:', error);
        alert('网络错误，请稍后重试');
    });
}

/**
 * 删除API Key
 */
function deleteApiKey(keyId) {
    if (!confirm('确定要删除这个API Key吗？删除后无法恢复，关联的邀请码也将无法通过API访问。')) {
        return;
    }
    
    fetch(`/api/keys/${keyId}`, {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('API Key删除成功');
            loadApiKeys();
        } else {
            alert('API Key删除失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('删除API Key错误:', error);
        alert('网络错误，请稍后重试');
    });
}

/**
 * 为API Key创建邀请码
 */
function createApiKeyCode(keyId) {
    const expiry = prompt('请输入邀请码有效期（天数）：', '7');
    if (!expiry || isNaN(expiry) || expiry < 1) {
        return;
    }
    
    fetch(`/api/keys/${keyId}/codes`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ expiry_days: parseInt(expiry) }),
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('邀请码创建成功！\n\n邀请码: ' + data.data.code);
            loadApiKeys(); // 刷新列表以更新邀请码数量
        } else {
            alert('邀请码创建失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('创建邀请码错误:', error);
        alert('网络错误，请稍后重试');
    });
}

/**
 * 查看API Key的邀请码
 */
function viewApiKeyCodes(keyId) {
    fetch(`/api/keys/${keyId}/codes`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.data.codes) {
            let message = `API Key "${data.data.api_key.name}" 的邀请码列表：\n\n`;
            if (data.data.codes.length === 0) {
                message += '暂无邀请码';
            } else {
                data.data.codes.forEach(code => {
                    const status = code.is_used ? '已使用' : 
                                  (new Date(code.expires_at) < new Date() ? '已过期' : '未使用');
                    message += `${code.code} - ${status} - ${formatDate(code.created_at)}\n`;
                });
            }
            alert(message);
        } else {
            alert('获取邀请码列表失败: ' + data.error);
        }
    })
    .catch(error => {
        console.error('获取邀请码列表错误:', error);
        alert('网络错误，请稍后重试');
    });
}

// 将changePage函数暴露为全局函数，以便HTML中的onclick调用
window.changePage = changePage;
window.changeUserPage = changeUserPage;
window.editUser = editUser;
window.deleteUser = deleteUser;
window.closeUserEditModal = closeUserEditModal;
window.createApiKey = createApiKey;
window.loadApiKeys = loadApiKeys;
window.copyApiKey = copyApiKey;
window.toggleApiKey = toggleApiKey;
window.deleteApiKey = deleteApiKey;
window.createApiKeyCode = createApiKeyCode;
window.viewApiKeyCodes = viewApiKeyCodes; 

/**
 * 复制邀请码
 */
function copyInviteCode(code) {
    if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(code).then(() => {
            showToast('邀请码已复制到剪贴板', 'success');
        }).catch(err => {
            console.error('复制失败:', err);
            fallbackCopyText(code);
        });
    } else {
        fallbackCopyText(code);
    }
}

/**
 * 后备复制方法
 */
function fallbackCopyText(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.opacity = '0';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        document.execCommand('copy');
        showToast('邀请码已复制到剪贴板', 'success');
    } catch (err) {
        console.error('复制失败:', err);
        showToast('复制失败，请手动复制', 'error');
    }
    
    document.body.removeChild(textArea);
}

/**
 * 删除单个邀请码
 */
function deleteInviteCode(codeId, code) {
    if (!confirm(`确定要删除邀请码 "${code}" 吗？删除后无法恢复。`)) {
        return;
    }
    
    fetch(`/api/invite/codes/${codeId}`, {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('邀请码删除成功', 'success');
            loadInviteCodes();
        } else {
            showToast(`删除失败: ${data.error}`, 'error');
        }
    })
    .catch(error => {
        console.error('删除邀请码错误:', error);
        showToast('删除失败，请稍后重试', 'error');
    });
}

/**
 * 批量删除邀请码
 */
function batchDeleteInviteCodes() {
    const checkboxes = document.querySelectorAll('.code-checkbox:checked');
    const codeIds = Array.from(checkboxes).map(checkbox => parseInt(checkbox.value));
    
    if (codeIds.length === 0) {
        showToast('请选择要删除的邀请码', 'warning');
        return;
    }
    
    if (!confirm(`确定要删除选中的 ${codeIds.length} 个邀请码吗？删除后无法恢复。`)) {
        return;
    }
    
    fetch('/api/invite/codes/batch', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ code_ids: codeIds }),
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            loadInviteCodes();
        } else {
            showToast(`批量删除失败: ${data.error}`, 'error');
        }
    })
    .catch(error => {
        console.error('批量删除邀请码错误:', error);
        showToast('批量删除失败，请稍后重试', 'error');
    });
}

/**
 * 全选/反选邀请码
 */
function toggleAllCodes(selectAll) {
    const checkboxes = document.querySelectorAll('.code-checkbox:not(:disabled)');
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll;
    });
    updateBatchActions();
}

/**
 * 更新批量操作按钮状态
 */
function updateBatchActions() {
    const checkboxes = document.querySelectorAll('.code-checkbox:checked');
    const batchDeleteBtn = document.getElementById('batch-delete-btn');
    const selectAllBtn = document.getElementById('select-all-btn');
    const selectAllHeader = document.getElementById('select-all-header');
    
    if (batchDeleteBtn) {
        batchDeleteBtn.disabled = checkboxes.length === 0;
    }
    
    // 更新全选按钮状态
    const allCheckboxes = document.querySelectorAll('.code-checkbox:not(:disabled)');
    const allChecked = allCheckboxes.length > 0 && checkboxes.length === allCheckboxes.length;
    
    if (selectAllBtn) {
        selectAllBtn.textContent = allChecked ? '取消全选' : '全选';
        selectAllBtn.onclick = () => toggleAllCodes(!allChecked);
    }
    
    // 同步头部复选框状态
    if (selectAllHeader) {
        selectAllHeader.checked = allChecked;
        selectAllHeader.indeterminate = checkboxes.length > 0 && !allChecked;
    }
}

/**
 * 显示提示消息
 */
function showToast(message, type = 'info') {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    // 添加样式
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 24px;
        border-radius: 4px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        animation: slideInRight 0.3s ease-out;
    `;
    
    // 根据类型设置背景色
    switch (type) {
        case 'success':
            toast.style.backgroundColor = '#28a745';
            break;
        case 'error':
            toast.style.backgroundColor = '#dc3545';
            break;
        case 'warning':
            toast.style.backgroundColor = '#ffc107';
            toast.style.color = '#212529';
            break;
        default:
            toast.style.backgroundColor = '#17a2b8';
    }
    
    document.body.appendChild(toast);
    
    // 3秒后自动消失
    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease-out';
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

/**
 * 删除单个核销记录
 */
function deleteRedeemCode(codeId, code) {
    if (!confirm(`确定要删除核销记录 "${code}" 吗？\n删除后邀请码将重置为未使用状态。`)) {
        return;
    }

    fetch(`/api/invite/redeem/${codeId}`, {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('核销记录删除成功', 'success');
            loadRedeemedCodes();
            loadDashboardData(); // 刷新统计数据
        } else {
            showToast(data.message || '删除失败', 'error');
        }
    })
    .catch(error => {
        console.error('删除核销记录错误:', error);
        showToast('网络错误，请稍后重试', 'error');
    });
}

/**
 * 批量删除核销记录
 */
function batchDeleteRedeemCodes() {
    const checkboxes = document.querySelectorAll('.redeem-checkbox:checked');
    const codeIds = Array.from(checkboxes).map(checkbox => parseInt(checkbox.value));

    if (codeIds.length === 0) {
        showToast('请选择要删除的核销记录', 'warning');
        return;
    }

    if (!confirm(`确定要删除选中的 ${codeIds.length} 个核销记录吗？\n删除后对应的邀请码将重置为未使用状态。`)) {
        return;
    }

    fetch('/api/invite/redeem/batch', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ code_ids: codeIds }),
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            loadRedeemedCodes();
            loadDashboardData(); // 刷新统计数据
        } else {
            showToast(data.message || '批量删除失败', 'error');
        }
    })
    .catch(error => {
        console.error('批量删除核销记录错误:', error);
        showToast('网络错误，请稍后重试', 'error');
    });
}

/**
 * 切换核销记录全选状态
 */
function toggleAllRedeemCodes(selectAll) {
    const checkboxes = document.querySelectorAll('.redeem-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll;
    });
    updateRedeemBatchActions();
}

/**
 * 更新核销记录批量操作按钮状态
 */
function updateRedeemBatchActions() {
    const checkboxes = document.querySelectorAll('.redeem-checkbox:checked');
    const batchDeleteBtn = document.getElementById('batch-delete-redeem-btn');
    const selectAllBtn = document.getElementById('select-all-redeem-btn');
    const selectAllHeader = document.getElementById('select-all-redeem-header');

    if (batchDeleteBtn) {
        batchDeleteBtn.disabled = checkboxes.length === 0;
    }

    // 更新全选按钮状态
    const allCheckboxes = document.querySelectorAll('.redeem-checkbox');
    const allChecked = allCheckboxes.length > 0 && checkboxes.length === allCheckboxes.length;

    if (selectAllBtn) {
        selectAllBtn.textContent = allChecked ? '取消全选' : '全选';
        selectAllBtn.onclick = () => toggleAllRedeemCodes(!allChecked);
    }

    if (selectAllHeader) {
        selectAllHeader.checked = allChecked;
        selectAllHeader.indeterminate = checkboxes.length > 0 && checkboxes.length < allCheckboxes.length;
    }
}

/**
 * 更新核销记录分页
 */
function updateRedeemPagination(totalPages, currentPage) {
    const pagination = document.getElementById('redeem-codes-pagination');
    if (!pagination || totalPages <= 1) {
        if (pagination) pagination.innerHTML = '';
        return;
    }

    let html = '';

    // 上一页
    if (currentPage > 1) {
        html += `<button class="btn btn-sm btn-outline" onclick="loadRedeemedCodes(${currentPage - 1})">上一页</button>`;
    }

    // 页码
    for (let i = 1; i <= totalPages; i++) {
        if (i === currentPage) {
            html += `<button class="btn btn-sm btn-primary">${i}</button>`;
        } else {
            html += `<button class="btn btn-sm btn-outline" onclick="loadRedeemedCodes(${i})">${i}</button>`;
        }
    }

    // 下一页
    if (currentPage < totalPages) {
        html += `<button class="btn btn-sm btn-outline" onclick="loadRedeemedCodes(${currentPage + 1})">下一页</button>`;
    }

    pagination.innerHTML = html;
}

// 暴露新的全局函数
window.copyInviteCode = copyInviteCode;
window.deleteInviteCode = deleteInviteCode;
window.batchDeleteInviteCodes = batchDeleteInviteCodes;
window.toggleAllCodes = toggleAllCodes;
window.deleteRedeemCode = deleteRedeemCode;
window.batchDeleteRedeemCodes = batchDeleteRedeemCodes;
window.toggleAllRedeemCodes = toggleAllRedeemCodes;