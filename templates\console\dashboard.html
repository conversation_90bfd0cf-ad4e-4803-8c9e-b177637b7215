<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>控制台 - 邀请码核销系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/modern-console.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 移动端侧边栏切换按钮 -->
    <button class="sidebar-toggle" id="sidebar-toggle">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="3" y1="6" x2="21" y2="6"></line>
            <line x1="3" y1="12" x2="21" y2="12"></line>
            <line x1="3" y1="18" x2="21" y2="18"></line>
        </svg>
    </button>

    <div class="dashboard">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h2>邀请码核销系统</h2>
                <p>管理控制台</p>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="#dashboard" class="nav-link active" data-section="dashboard-section">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="3" y="3" width="7" height="7"></rect>
                                <rect x="14" y="3" width="7" height="7"></rect>
                                <rect x="14" y="14" width="7" height="7"></rect>
                                <rect x="3" y="14" width="7" height="7"></rect>
                            </svg>
                            仪表盘
                        </a>
                    </li>
                    <li>
                        <a href="#invite-codes" class="nav-link" data-section="invite-codes-section">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                                <line x1="8" y1="21" x2="16" y2="21"></line>
                                <line x1="12" y1="17" x2="12" y2="21"></line>
                            </svg>
                            邀请码管理
                        </a>
                    </li>
                    <li>
                        <a href="#redeem" class="nav-link" data-section="redeem-section">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="9,11 12,14 22,4"></polyline>
                                <path d="m21,3-6.5,6.5L11,7"></path>
                            </svg>
                            核销邀请码
                        </a>
                    </li>
                    <li>
                        <a href="#api-keys" class="nav-link" data-section="api-keys-section">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 2l-2 2m-7.61 7.61a5.5 5.5 0 1 1-7.778 7.778 5.5 5.5 0 0 1 7.777-7.777zm0 0L15.5 7.5m0 0l3 3L22 7l-3-3m-3.5 3.5L19 4"></path>
                            </svg>
                            API 管理
                        </a>
                    </li>
                    <li>
                        <a href="#users" class="nav-link admin-only" data-section="users-section" id="users-nav" style="display: none;">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                <circle cx="12" cy="7" r="4"></circle>
                            </svg>
                            用户管理
                        </a>
                    </li>
                    <li>
                        <a href="#profile" class="nav-link" data-section="profile-section">
                            <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path>
                                <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                            个人设置
                        </a>
                    </li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <p>&copy; 2024 邀请码核销系统</p>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 内容头部 -->
            <div class="content-header">
                <h1 id="page-title">仪表盘</h1>
                <div class="user-info">
                    <span id="user-name">加载中...</span>
                    <button class="btn btn-outline" id="btn-logout">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                            <polyline points="16,17 21,12 16,7"></polyline>
                            <line x1="21" y1="12" x2="9" y2="12"></line>
                        </svg>
                        退出登录
                    </button>
                </div>
            </div>

            <!-- 内容主体 -->
            <div class="content-body">
                <!-- 仪表盘部分 -->
                <div class="content-section active" id="dashboard-section">
                    <!-- 统计卡片 -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <h3>总邀请码</h3>
                            <div class="stat-value" id="total-codes">-</div>
                            <div class="stat-label">已创建的邀请码总数</div>
                        </div>
                        <div class="stat-card">
                            <h3>已使用</h3>
                            <div class="stat-value" id="used-codes">-</div>
                            <div class="stat-label">已核销的邀请码</div>
                        </div>
                        <div class="stat-card">
                            <h3>未使用</h3>
                            <div class="stat-value" id="unused-codes">-</div>
                            <div class="stat-label">可用的邀请码</div>
                        </div>
                        <div class="stat-card">
                            <h3>使用率</h3>
                            <div class="stat-value" id="usage-rate">-%</div>
                            <div class="stat-label">邀请码使用率</div>
                        </div>
                    </div>

                    <!-- 最近邀请码 -->
                    <div class="card">
                        <div class="card-header">
                            <h2>最近邀请码</h2>
                            <button class="btn btn-primary" onclick="showSection('invite-codes-section')">
                                查看全部
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-container">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>邀请码</th>
                                            <th>创建时间</th>
                                            <th>过期时间</th>
                                            <th>状态</th>
                                        </tr>
                                    </thead>
                                    <tbody id="recent-codes-table">
                                        <tr>
                                            <td colspan="4">
                                                <div class="loader"></div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 邀请码管理部分 -->
                <div class="content-section" id="invite-codes-section">
                    <!-- 生成邀请码 -->
                    <div class="card">
                        <div class="card-header">
                            <h2>生成邀请码</h2>
                        </div>
                        <div class="card-body">
                            <form id="generate-code-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="expiry-days">有效期（天）</label>
                                        <input type="number" id="expiry-days" name="expiry_days" class="form-control" min="1" max="30" value="7">
                                    </div>
                                    <div class="form-group">
                                        <label for="generate-count">生成数量</label>
                                        <input type="number" id="generate-count" name="count" class="form-control" min="1" max="50" value="1">
                                        <small class="form-text text-muted">单次最多生成50个（普通用户20个）</small>
                                    </div>
                                </div>
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <line x1="12" y1="8" x2="12" y2="16"></line>
                                            <line x1="8" y1="12" x2="16" y2="12"></line>
                                        </svg>
                                        生成邀请码
                                    </button>
                                </div>
                            </form>
                            
                            <!-- 批量生成结果展示 -->
                            <div id="batch-generate-result" class="batch-result" style="display: none;">
                                <div class="batch-result-header">
                                    <h4>批量生成结果</h4>
                                    <div class="batch-result-actions">
                                        <button id="copy-all-codes" class="btn btn-sm btn-secondary">复制全部</button>
                                        <button id="close-batch-result" class="btn btn-sm btn-secondary">关闭</button>
                                    </div>
                                </div>
                                <div class="batch-result-content">
                                    <div class="batch-result-summary">
                                        <span id="batch-result-count">0</span> 个邀请码已生成
                                    </div>
                                    <div class="batch-codes-container">
                                        <div id="batch-codes-list" class="batch-codes-list"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 邀请码列表 -->
                    <div class="card">
                        <div class="card-header">
                            <h2>邀请码列表</h2>
                            <div class="card-header-actions">
                                <select id="code-status-filter" class="form-control" style="width: auto;">
                                    <option value="all">全部状态</option>
                                    <option value="unused">未使用</option>
                                    <option value="used">已使用</option>
                                    <option value="expired">已过期</option>
                                </select>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 批量操作工具栏 -->
                            <div class="batch-toolbar" style="margin-bottom: 15px; padding: 10px; background-color: #f8f9fa; border-radius: 4px;">
                                <div class="batch-actions">
                                    <button id="select-all-btn" class="btn btn-sm btn-secondary" onclick="toggleAllCodes(false)">全选</button>
                                    <button id="batch-delete-btn" class="btn btn-sm btn-danger" onclick="batchDeleteInviteCodes()" disabled>批量删除</button>
                                    <span class="batch-info" style="margin-left: 15px; color: #6c757d; font-size: 14px;">
                                        提示：只能删除未使用和已过期的邀请码
                                    </span>
                                </div>
                            </div>
                            
                            <div class="table-container">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th style="width: 40px;">
                                                <input type="checkbox" id="select-all-header">
                                            </th>
                                            <th>邀请码</th>
                                            <th>创建时间</th>
                                            <th>过期时间</th>
                                            <th>状态</th>
                                            <th>使用者</th>
                                            <th>核销时间</th>
                                            <th style="width: 120px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="codes-table">
                                        <tr>
                                            <td colspan="8">
                                                <div class="loader"></div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="pagination" id="codes-pagination"></div>
                        </div>
                    </div>
                </div>

                <!-- API管理部分 -->
                <div class="content-section" id="api-keys-section">
                    <!-- 创建API Key -->
                    <div class="card">
                        <div class="card-header">
                            <h2>创建API Key</h2>
                        </div>
                        <div class="card-body">
                            <form id="create-api-key-form">
                                <div class="form-group">
                                    <label for="api-key-name">API Key名称</label>
                                    <input type="text" id="api-key-name" name="name" class="form-control" placeholder="请输入API Key名称，如：我的应用" required maxlength="128">
                                </div>
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <circle cx="12" cy="12" r="10"></circle>
                                            <line x1="12" y1="8" x2="12" y2="16"></line>
                                            <line x1="8" y1="12" x2="16" y2="12"></line>
                                        </svg>
                                        创建API Key
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- API Key列表 -->
                    <div class="card">
                        <div class="card-header">
                            <h2>API Key列表</h2>
                        </div>
                        <div class="card-body">
                            <div class="table-container">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>名称</th>
                                            <th>API Key</th>
                                            <th>状态</th>
                                            <th>邀请码数量</th>
                                            <th>创建时间</th>
                                            <th>最后使用</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="api-keys-table">
                                        <tr>
                                            <td colspan="7">
                                                <div class="loader"></div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="pagination" id="api-keys-pagination"></div>
                        </div>
                    </div>

                    <!-- API使用说明 -->
                    <div class="card">
                        <div class="card-header">
                            <h2>API使用说明</h2>
                        </div>
                        <div class="card-body">
                            <h3>核销邀请码</h3>
                            <p>通过API核销邀请码，需要在请求头中携带API Key：</p>
                            <pre><code>POST /api/redeem
Headers: X-API-Key: your-api-key
Content-Type: application/json

{
    "code": "ABCD1234"
}</code></pre>

                            <h3>查询邀请码</h3>
                            <p>查询特定邀请码的状态：</p>
                            <pre><code>GET /api/codes/ABCD1234
Headers: X-API-Key: your-api-key</code></pre>

                            <h3>批量生成邀请码</h3>
                            <p>通过API批量生成邀请码：</p>
                            <pre><code>POST /api/invite/batch-generate
Headers: X-API-Key: your-api-key
Content-Type: application/json

{
    "count": 10,
    "expiry_days": 7
}</code></pre>

                            <h3>获取邀请码列表</h3>
                            <p>获取API Key下的所有邀请码：</p>
                            <pre><code>GET /api/codes?status=unused&page=1&per_page=20
Headers: X-API-Key: your-api-key</code></pre>

                            <h3>响应格式</h3>
                            <p>所有API响应都采用统一的JSON格式：</p>
                            <pre><code>{
    "success": true,
    "message": "操作成功",
    "data": {...}
}</code></pre>
                        </div>
                    </div>
                </div>

                <!-- 核销邀请码部分 -->
                <div class="content-section" id="redeem-section">
                    <!-- 核销邀请码 -->
                    <div class="card">
                        <div class="card-header">
                            <h2>核销邀请码</h2>
                        </div>
                        <div class="card-body">
                            <form id="redeem-code-form">
                                <div class="form-group">
                                    <label for="invite-code">邀请码</label>
                                    <input type="text" id="invite-code" name="code" class="form-control" placeholder="请输入8位邀请码" required maxlength="8">
                                </div>
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <polyline points="20,6 9,17 4,12"></polyline>
                                        </svg>
                                        核销邀请码
                                    </button>
                                </div>
                            </form>
                            <div id="redeem-result"></div>
                        </div>
                    </div>

                    <!-- 核销记录 -->
                    <div class="card">
                        <div class="card-header">
                            <h2>核销记录</h2>
                            <div class="card-header-actions">
                                <select id="redeem-status-filter" class="form-control" style="width: auto;">
                                    <option value="all">全部记录</option>
                                    <option value="user">用户核销</option>
                                    <option value="api">API核销</option>
                                </select>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 批量操作工具栏 -->
                            <div class="batch-toolbar" style="margin-bottom: 15px; padding: 10px; background-color: #f8f9fa; border-radius: 4px;">
                                <div class="batch-actions">
                                    <button id="select-all-redeem-btn" class="btn btn-sm btn-secondary" onclick="toggleAllRedeemCodes(false)">全选</button>
                                    <button id="batch-delete-redeem-btn" class="btn btn-sm btn-danger" onclick="batchDeleteRedeemCodes()" disabled>批量删除</button>
                                    <span class="batch-info" style="margin-left: 15px; color: #6c757d; font-size: 14px;">
                                        提示：删除核销记录会将邀请码状态重置为未使用
                                    </span>
                                </div>
                            </div>

                            <div class="table-container">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th style="width: 40px;">
                                                <input type="checkbox" id="select-all-redeem-header">
                                            </th>
                                            <th>邀请码</th>
                                            <th>核销时间</th>
                                            <th>核销方式</th>
                                            <th>创建者</th>
                                            <th style="width: 120px;">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="redeemed-codes-table">
                                        <tr>
                                            <td colspan="6">
                                                <div class="loader"></div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="pagination" id="redeem-codes-pagination"></div>
                        </div>
                    </div>
                </div>

                <!-- 用户管理部分 -->
                <div class="content-section" id="users-section">
                    <!-- 用户列表 -->
                    <div class="card">
                        <div class="card-header">
                            <h2>用户列表</h2>
                            <button class="btn btn-primary" onclick="showAddUserModal()">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                                    <circle cx="9" cy="7" r="4"></circle>
                                    <line x1="19" y1="8" x2="19" y2="14"></line>
                                    <line x1="22" y1="11" x2="16" y2="11"></line>
                                </svg>
                                添加用户
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="table-container">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>用户名</th>
                                            <th>邮箱</th>
                                            <th>注册时间</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="users-table">
                                        <tr>
                                            <td colspan="5">
                                                <div class="loader"></div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="pagination" id="users-pagination"></div>
                        </div>
                    </div>
                </div>

                <!-- 个人设置部分 -->
                <div class="content-section" id="profile-section">
                    <!-- 个人信息 -->
                    <div class="card">
                        <div class="card-header">
                            <h2>个人信息</h2>
                        </div>
                        <div class="card-body">
                            <form id="profile-form">
                                <div class="form-group">
                                    <label for="profile-username">用户名</label>
                                    <input type="text" id="profile-username" name="username" class="form-control" disabled>
                                </div>
                                <div class="form-group">
                                    <label for="profile-email">邮箱</label>
                                    <input type="email" id="profile-email" name="email" class="form-control">
                                </div>
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                                            <polyline points="17,21 17,13 7,13 7,21"></polyline>
                                            <polyline points="7,3 7,8 15,8"></polyline>
                                        </svg>
                                        保存更改
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- 修改密码 -->
                    <div class="card">
                        <div class="card-header">
                            <h2>修改密码</h2>
                        </div>
                        <div class="card-body">
                            <form id="password-form">
                                <div class="form-group">
                                    <label for="current-password">当前密码</label>
                                    <input type="password" id="current-password" name="current_password" class="form-control" required>
                                </div>
                                <div class="form-group">
                                    <label for="new-password">新密码</label>
                                    <input type="password" id="new-password" name="new_password" class="form-control" required>
                                </div>
                                <div class="form-group">
                                    <label for="confirm-password">确认新密码</label>
                                    <input type="password" id="confirm-password" name="confirm_password" class="form-control" required>
                                </div>
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                                            <circle cx="12" cy="16" r="1"></circle>
                                            <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                                        </svg>
                                        修改密码
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加用户模态框 -->
    <div id="add-user-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>添加新用户</h3>
                <button class="modal-close" onclick="hideAddUserModal()">×</button>
            </div>
            <div class="modal-body">
                <form id="add-user-form">
                    <div class="form-group">
                        <label for="new-username">用户名</label>
                        <input type="text" id="new-username" name="username" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="new-email">邮箱</label>
                        <input type="email" id="new-email" name="email" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="new-password">密码</label>
                        <input type="password" id="new-password" name="password" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label style="display: flex; align-items: center; gap: 8px;">
                            <input type="checkbox" id="new-is-admin" name="is_admin">
                            设为管理员
                        </label>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="hideAddUserModal()">取消</button>
                        <button type="submit" class="btn btn-primary">添加用户</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 用户编辑模态框 -->
    <div id="user-edit-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑用户</h3>
                <button class="modal-close" onclick="closeUserEditModal()">×</button>
            </div>
            <div class="modal-body">
                <form id="edit-user-form">
                    <input type="hidden" id="edit-user-id" name="user_id">
                    <div class="form-group">
                        <label for="edit-username">用户名</label>
                        <input type="text" id="edit-username" name="username" class="form-control" disabled>
                    </div>
                    <div class="form-group">
                        <label for="edit-email">邮箱</label>
                        <input type="email" id="edit-email" name="email" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label style="display: flex; align-items: center; gap: 8px;">
                            <input type="checkbox" id="edit-is-admin" name="is_admin">
                            管理员权限
                        </label>
                    </div>
                    <div class="form-group">
                        <label for="edit-new-password">重置密码（可选）</label>
                        <input type="password" id="edit-new-password" name="password" class="form-control" placeholder="留空则不修改密码">
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeUserEditModal()">取消</button>
                        <button type="submit" class="btn btn-primary">保存更改</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- API Key批量生成邀请码模态框 -->
    <div id="api-batch-generate-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>批量生成邀请码</h3>
                <button class="modal-close" onclick="closeApiBatchGenerateModal()">×</button>
            </div>
            <div class="modal-body">
                <form id="api-batch-generate-form">
                    <input type="hidden" id="api-batch-key-id" name="api_key_id">
                    <input type="hidden" id="api-batch-key-name" name="api_key_name">

                    <div class="form-group">
                        <label>API Key信息</label>
                        <div style="padding: 12px; background: var(--bg-tertiary); border-radius: var(--radius-md); margin-bottom: 16px;">
                            <div style="font-weight: 600; margin-bottom: 4px;">名称: <span id="api-batch-display-name">-</span></div>
                            <div style="font-family: var(--font-mono); font-size: var(--text-sm); color: var(--text-secondary);">
                                Key: <span id="api-batch-display-key">-</span>
                            </div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="api-batch-count">生成数量</label>
                            <input type="number" id="api-batch-count" name="count" class="form-control" min="1" max="100" value="10" required>
                            <small class="form-text">单次最多生成100个邀请码</small>
                        </div>
                        <div class="form-group">
                            <label for="api-batch-expiry">有效期（天）</label>
                            <input type="number" id="api-batch-expiry" name="expiry_days" class="form-control" min="1" max="365" value="7" required>
                            <small class="form-text">邀请码的有效期，1-365天</small>
                        </div>
                    </div>

                    <div class="form-group">
                        <label style="display: flex; align-items: center; gap: 8px;">
                            <input type="checkbox" id="api-batch-auto-download" name="auto_download" checked>
                            生成后自动下载邀请码列表
                        </label>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeApiBatchGenerateModal()">取消</button>
                        <button type="submit" class="btn btn-primary">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"></circle>
                                <line x1="12" y1="8" x2="12" y2="16"></line>
                                <line x1="8" y1="12" x2="16" y2="12"></line>
                            </svg>
                            开始生成
                        </button>
                    </div>
                </form>

                <!-- 批量生成结果展示 -->
                <div id="api-batch-result" class="batch-result" style="display: none;">
                    <div class="batch-result-header">
                        <h4>批量生成结果</h4>
                        <div class="batch-result-actions">
                            <button id="api-batch-copy-all" class="btn btn-sm btn-secondary">复制全部</button>
                            <button id="api-batch-download" class="btn btn-sm btn-primary">下载列表</button>
                            <button id="api-batch-close-result" class="btn btn-sm btn-secondary">关闭</button>
                        </div>
                    </div>
                    <div class="batch-result-content">
                        <div class="batch-result-summary">
                            为 API Key "<span id="api-batch-result-key-name">-</span>" 成功生成 <span id="api-batch-result-count">0</span> 个邀请码
                        </div>
                        <div class="batch-codes-container">
                            <div id="api-batch-codes-list" class="batch-codes-list"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/console.js') }}"></script>
    <script>
        // 现代化控制台脚本
        document.addEventListener('DOMContentLoaded', function() {
            // 侧边栏切换
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const sidebar = document.getElementById('sidebar');
            
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.toggle('open');
            });
            
            // 点击外部关闭侧边栏
            document.addEventListener('click', function(e) {
                if (window.innerWidth <= 768 && !sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                    sidebar.classList.remove('open');
                }
            });
            
            // 检查管理员状态
            setTimeout(function() {
                const userDataStr = sessionStorage.getItem('user');
                if (userDataStr) {
                    const userData = JSON.parse(userDataStr);
                    if (userData.is_admin) {
                        const usersNav = document.getElementById('users-nav');
                        if (usersNav) {
                            usersNav.style.display = 'block';
                        }
                    }
                }
            }, 100);

            // API批量生成相关事件监听
            const apiBatchCopyAllBtn = document.getElementById('api-batch-copy-all');
            const apiBatchDownloadBtn = document.getElementById('api-batch-download');
            const apiBatchCloseResultBtn = document.getElementById('api-batch-close-result');

            if (apiBatchCopyAllBtn) {
                apiBatchCopyAllBtn.addEventListener('click', copyApiBatchCodes);
            }

            if (apiBatchDownloadBtn) {
                apiBatchDownloadBtn.addEventListener('click', downloadApiBatchCodes);
            }

            if (apiBatchCloseResultBtn) {
                apiBatchCloseResultBtn.addEventListener('click', function() {
                    document.getElementById('api-batch-result').style.display = 'none';
                });
            }

            // API批量生成表单提交事件
            const apiBatchForm = document.getElementById('api-batch-generate-form');
            if (apiBatchForm) {
                apiBatchForm.addEventListener('submit', handleApiBatchGenerate);
            }

            // 核销记录相关事件监听
            setupRedeemCodeEvents();
        });
        
        // 显示指定内容区域（保留给其他地方调用）
        function showSection(sectionId) {
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => {
                section.classList.remove('active');
            });
            
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.classList.add('active');
            }
            
            // 加载对应数据
            if (typeof loadDashboardData === 'function') {
                loadDashboardData();
            }
        }
        
        // 显示添加用户模态框
        function showAddUserModal() {
            document.getElementById('add-user-modal').style.display = 'flex';
        }
        
        // 隐藏添加用户模态框
        function hideAddUserModal() {
            document.getElementById('add-user-modal').style.display = 'none';
            document.getElementById('add-user-form').reset();
        }
        
        // 关闭用户编辑模态框
        function closeUserEditModal() {
            document.getElementById('user-edit-modal').style.display = 'none';
            document.getElementById('edit-user-form').reset();
        }

        // 显示API Key批量生成模态框
        function showApiBatchGenerateModal(apiKeyId, apiKeyName, apiKey) {
            document.getElementById('api-batch-key-id').value = apiKeyId;
            document.getElementById('api-batch-key-name').value = apiKeyName;
            document.getElementById('api-batch-display-name').textContent = apiKeyName;
            document.getElementById('api-batch-display-key').textContent = apiKey;

            // 重置表单
            document.getElementById('api-batch-generate-form').reset();
            document.getElementById('api-batch-key-id').value = apiKeyId;
            document.getElementById('api-batch-key-name').value = apiKeyName;
            document.getElementById('api-batch-count').value = 10;
            document.getElementById('api-batch-expiry').value = 7;
            document.getElementById('api-batch-auto-download').checked = true;

            // 隐藏结果区域
            document.getElementById('api-batch-result').style.display = 'none';

            // 显示模态框
            document.getElementById('api-batch-generate-modal').style.display = 'flex';
        }

        // 关闭API Key批量生成模态框
        function closeApiBatchGenerateModal() {
            document.getElementById('api-batch-generate-modal').style.display = 'none';
            document.getElementById('api-batch-generate-form').reset();
            document.getElementById('api-batch-result').style.display = 'none';
        }

        // 复制API批量生成的所有邀请码
        function copyApiBatchCodes() {
            const codeElements = document.querySelectorAll('#api-batch-codes-list .batch-code-text');
            const codes = Array.from(codeElements).map(el => el.textContent).join('\n');

            if (navigator.clipboard) {
                navigator.clipboard.writeText(codes).then(() => {
                    showToast('已复制所有邀请码到剪贴板', 'success');
                }).catch(() => {
                    fallbackCopyText(codes);
                });
            } else {
                fallbackCopyText(codes);
            }
        }

        // 下载API批量生成的邀请码列表
        function downloadApiBatchCodes() {
            const codeElements = document.querySelectorAll('#api-batch-codes-list .batch-code-text');
            const codes = Array.from(codeElements).map(el => el.textContent);
            const apiKeyName = document.getElementById('api-batch-result-key-name').textContent;
            const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');

            const content = codes.join('\n');
            const blob = new Blob([content], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = `invite-codes-${apiKeyName}-${timestamp}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showToast('邀请码列表已下载', 'success');
        }

        // 辅助函数：备用复制方法
        function fallbackCopyText(text) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                showToast('已复制所有邀请码到剪贴板', 'success');
            } catch (err) {
                showToast('复制失败，请手动复制', 'error');
            }
            document.body.removeChild(textArea);
        }

        // 处理API批量生成表单提交
        function handleApiBatchGenerate(event) {
            event.preventDefault();

            const keyId = document.getElementById('api-batch-key-id').value;
            const count = parseInt(document.getElementById('api-batch-count').value);
            const expiryDays = parseInt(document.getElementById('api-batch-expiry').value);
            const autoDownload = document.getElementById('api-batch-auto-download').checked;
            const apiKeyName = document.getElementById('api-batch-key-name').value;

            // 显示加载状态
            const submitBtn = event.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<div class="spinner-border spinner-border-sm"></div> 生成中...';
            submitBtn.disabled = true;

            // 调用API
            const token = localStorage.getItem('access_token');
            fetch(`/api/keys/${keyId}/codes/batch`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    count: count,
                    expiry_days: expiryDays
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 显示结果
                    showApiBatchResult(data.data.codes, apiKeyName, autoDownload);
                    showToast(`成功生成 ${data.data.count} 个邀请码`, 'success');
                } else {
                    showToast(data.message || '生成失败', 'error');
                }
            })
            .catch(error => {
                console.error('批量生成失败:', error);
                showToast('生成失败，请稍后重试', 'error');
            })
            .finally(() => {
                // 恢复按钮状态
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        }

        // 显示批量生成结果
        function showApiBatchResult(codes, apiKeyName, autoDownload) {
            document.getElementById('api-batch-result-key-name').textContent = apiKeyName;
            document.getElementById('api-batch-result-count').textContent = codes.length;

            const codesList = document.getElementById('api-batch-codes-list');
            codesList.innerHTML = '';

            codes.forEach((codeObj, index) => {
                const code = codeObj.code || codeObj; // 兼容不同的数据格式
                const codeItem = document.createElement('div');
                codeItem.className = 'batch-code-item';
                codeItem.innerHTML = `
                    <span class="batch-code-text">${code}</span>
                    <button class="btn btn-sm btn-outline" onclick="copyCode('${code}')">复制</button>
                `;
                codesList.appendChild(codeItem);
            });

            document.getElementById('api-batch-result').style.display = 'block';

            // 自动下载
            if (autoDownload) {
                setTimeout(() => {
                    downloadApiBatchCodes();
                }, 500);
            }
        }

        // 复制单个邀请码
        function copyCode(code) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(code).then(() => {
                    showToast(`已复制邀请码: ${code}`, 'success');
                });
            } else {
                fallbackCopyText(code);
            }
        }

        // 设置核销记录事件监听器
        function setupRedeemCodeEvents() {
            // 为动态添加的核销记录复选框添加事件监听器
            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('redeem-checkbox')) {
                    updateRedeemBatchActions();
                }
            });

            // 头部全选复选框事件
            const selectAllRedeemHeader = document.getElementById('select-all-redeem-header');
            if (selectAllRedeemHeader) {
                selectAllRedeemHeader.addEventListener('change', function() {
                    toggleAllRedeemCodes(this.checked);
                });
            }

            // 状态筛选器事件
            const redeemStatusFilter = document.getElementById('redeem-status-filter');
            if (redeemStatusFilter) {
                redeemStatusFilter.addEventListener('change', function() {
                    loadRedeemedCodes(1, this.value);
                });
            }
        }

        // 显示提示消息
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type}`;
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.animation = 'slideOutRight 0.3s ease-out forwards';
                setTimeout(() => {
                    if (document.body.contains(toast)) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>